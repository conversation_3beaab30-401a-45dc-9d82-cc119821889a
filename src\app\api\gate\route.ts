import { NextRequest, NextResponse } from "next/server";
import { initAdmin } from "../../../../firebaseAdminConfig";
import { verifyAuth } from "@/lib/authMiddleware";
import { SendMailHandler } from "@/lib/api-gateway-handlers/handlers";
import { NotificationHandlerManager } from "@/lib/api-gateway-handlers/notification-handlers";

// API GATEWAY

const handlers: Record<
  string,
  {
    method: "GET" | "POST";
    authRequired: boolean;
    handler: (body: any, uid?: string) => Promise<any>;
  }
> = {
    
  ping: {
    method: "GET",
    authRequired: false,
    handler: async () => {
      return { success: true, message: "pong" };
    },
  },

   /**
   * Mail handler's
   */

  sendMail: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await Send<PERSON>ailHandler(body, uid);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },
  sendResetPasswordMail: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await SendMailHandler(body, uid);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  /**
   * Notification handler's
   */

  CreateNotification: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await NotificationHandlerManager.getInstance()?.CreateNotification(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  UserUnreadNotificationCount: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await NotificationHandlerManager.getInstance()?.UserUnreadNotificationCount(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetNotificationsByUserId: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await NotificationHandlerManager.getInstance()?.GetNotificationsByUserId(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  BulkCreateNotificationsAndUpdateUnreadCounts: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp =
          await NotificationHandlerManager.getInstance()?.BulkCreateNotificationsAndUpdateUnreadCounts(
            body?.payload
          );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },


  /**
   * 
   * 
   */
  
  
  

  // --------- USERS ----------------------
  //   getAllUsers
  //   getFollowersIds
  //   getUserById // tricky
  //   getUserByEventId
  //   getUserByServicesId
  //   getUserIdByPostServiceEvent
  //   getUsersByCategory
  //   getUsersByCategoryWithPost
  //   sendResetPassword ---> add in MailService
  //   checkUserNameExists
  //   getUserIdByProfileName
  //   getProfileNameByUserId
  //   checkUserStripeId
  //   GetUserStripeId
  //   GetUserInfo
  //.  updateUser
  //

  // <deleteUserDetails> and all shole in backend last

  //
};

export async function POST(request: NextRequest) {
  try {
    await initAdmin();

    const body = await request.json();
    console.log({ body });

    const type = body.type;

    if (!type || !handlers[type]) {
      return NextResponse.json({ success: false, error: "Invalid API type" }, { status: 400 });
    }

    const { authRequired, handler } = handlers[type];

    // Run auth check if needed
    let uid: string | undefined;

    if (authRequired) {
      const auth = await verifyAuth(request);
      if ("status" in auth) return auth; // error response
      uid = auth.uid;
    }
    // Dispatch to handler
    const response = await handler(body, uid);

    return NextResponse.json({ success: true, message: response });
  } catch (error) {
    console.error({ error });
    return NextResponse.json(
      {
        success: false,
        error: "Failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
