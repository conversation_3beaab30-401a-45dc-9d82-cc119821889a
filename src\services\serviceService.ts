import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  query,
  where,
  orderBy,
  setDoc,
  Timestamp,
  documentId,
  arrayRemove,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { getAuth } from "firebase/auth";
import { FollowerManager } from "./followServices";
import { NotificationEvents, NotificationManager } from "./notificationService";
import { PublishingDateFilter } from "./filtersServices";
import { filterPostsByPublishingDate } from "./postService";

export interface Service {
  id?: string; // Optional, as it will be generated
  category: string;
  description: string;
  duration: string;
  price: string;
  title: string;
  deleted?: boolean;
  currency?: string;
  customizations?: string[]; // customizations ids
  customizations_array?: string[];
  created_at?:Timestamp
  customizationsModels?:CustomizationItem[]
}
export interface CustomizationItem {
  id: string;
  title: string;
  description?: string;
  price: string | number;
  duration?: string | number;
  media?: string[] | any[];
  currency?: string;
}

export interface CustomizationInput {
  id?: string;
  description: string;
  duration: string;
  media?: string[] | any;
  price: string | number;
  title: string;
  currency?: string;
}

// udpated check
// Create a new service (Add a new post)
export const createService = async (
  serviceData: any,
  customizations: Array<CustomizationInput> = []
) => {
  try {
    const { db } = await initFirebase();
    const servicesRef = collection(db, "services");
    const newServiceRef = doc(servicesRef);
    const _id = newServiceRef.id;

    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      return { success: false, id: null };
    }

    // First, create the service WITHOUT customizations
    await setDoc(newServiceRef, {
      id: _id,
      ...serviceData,
      created_at: serverTimestamp(),
      customizations: [], // empty initially
    });

    let customizationIds: string[] = [];

    if (customizations.length > 0) {
      const customizationsRef = collection(db, "services", _id, "customizations");

      const promises = customizations.map(async (c) => {
      const customRef = doc(customizationsRef);


       await setDoc(customRef, {
      id: c?.id ?? customRef.id,
      ...c,
      updated_at: serverTimestamp(),
    });
        // const customizationDocRef = await addDoc(customizationsRef, {
        //   ...c,
        //   id:customRef?.id,
        //   updated_at: serverTimestamp(),
        // });
        return customRef.id;

      });

      customizationIds = await Promise.all(promises);

      // Now update the service document to add the customization IDs
      await setDoc(newServiceRef, { customizations: customizationIds }, { merge: true });
    }

    // notification
    const followers:any[] = await FollowerManager.getInstance().GetFollowersByUserId(user?.uid);

    NotificationManager.getInstance().BulkCreateNotificationsAndUpdateUnreadCounts({
      event:NotificationEvents.SERVICE_UPLOAD , 
      followers:followers?.map((c)=>{
        return {
          id:c?.id
        }
      }),
      userId:user?.uid , 
    })    

    return { success: true, id: _id };
  } catch (error) {
    console.error("Error creating service:", error);
    return { success: false, error: "Failed to create service" };
  }
};

// check delete
// Get all services
export const getAllServices = async () => {
  try {
    const { db } = await initFirebase();

    const servicesRef = collection(db, "services");
    const querySnapshot = await getDocs(servicesRef);

    const services: Service[] = querySnapshot.docs
      .map((doc) => ({ id: doc.id, ...doc.data() }) as Service)
      .filter((service) => service.deleted !== true);

    return { success: true, services };
  } catch (error) {
    // console.error("Error fetching services:", error);
    return { success: false, error: "Failed to fetch services" };
  }
};

// not used
export const getCustomisations = async () => {
  try {
    const { db } = await initFirebase();

    const customizationsRef = collection(db, "services", "oAhNtSsuZEc0dFleqG1d", "customizations");
    const snapshot = await getDocs(customizationsRef);

    const customizations = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return { success: true, customizations };
  } catch (error) {
    return { success: false, error: "Failed to fetch customizations" };
  }
};

// Get a service by ID
// updated check
export const getServiceById = async (id: string) => {
  try {
    const { db } = await initFirebase();

    const serviceRef = doc(db, "services", id);
    const docSnap = await getDoc(serviceRef);

    if (!docSnap.exists()) {
      return { success: false, error: "Service not found" };
    }

    const serviceData = docSnap.data();
    const customizationIds = serviceData?.customizations || [];

    let customizations_array: CustomizationInput[] = [];

    if (customizationIds.length > 0) {
      const customizationsRef = collection(db, "services", id, "customizations");

      const allCustomizationsSnap = await getDocs(customizationsRef);

      customizations_array = allCustomizationsSnap.docs
        .filter((d) => customizationIds.includes(d.id)) // only keep the ones in the array
        .map((d) => ({
          id: d.id,
          ...(d.data() as CustomizationInput),
        }));
    }

    return {
      success: true,
      service: {
        id: docSnap.id,
        ...serviceData,
        customizations_array,
      },
    };
  } catch (error) {
    console.error("Error fetching service by ID:", error);
    return { success: false, error: "Failed to fetch service" };
  }
};

// updated check
//🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀  use this for getting service by userId
export const getServicesByUserId = async (userId: string) => {
  try {
    const { db } = await initFirebase();

    // Step 1: Fetch the user document
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return { success: false, error: "User not found" };
    }

    // Step 2: Extract services array from user document
    const userData = userSnap.data();
    const serviceIds = userData?.services ?? [];

    const currency = userData?.currency ?? "gbp";

    if (serviceIds.length === 0) {
      return { success: true, services: [] };
    }
    const serviceDetails = await fetchServicesInBatches(serviceIds, userId, currency);

    // // Step 3: Batch fetch service details
    // const batchPromises = serviceIds.map(async (id:any) => {
    //   const serviceRef = doc(db, "services", id);
    //   const docSnap = await getDoc(serviceRef);
    //   return docSnap.exists() ? { id: docSnap.id, ...docSnap.data(), user_id: userId } : null;
    // });

    // const serviceDetails = (await Promise.all(batchPromises)).filter((service) => service !== null);

    return { success: true, services: serviceDetails };
  } catch (error) {
    console.error("Error fetching user services:", error);
    return { success: false, error: "Failed to fetch user services" };
  }
};

const fetchServicesInBatches = async (
  serviceIds: string[],
  userId: string,
  currency: string,
  batchSize = 10
) => {
  const serviceDetails: any[] = [];
  const { db } = await initFirebase();

  for (let i = 0; i < serviceIds.length; i += batchSize) {
    const batchIds = serviceIds.slice(i, i + batchSize);

    const servicesQuery = query(collection(db, "services"), where("__name__", "in", batchIds));
    const querySnapshot = await getDocs(servicesQuery);

    // For each service document, also fetch its subcollection "customizations"
    for (const doc of querySnapshot.docs) {
      const serviceId = doc.id;
      const serviceData = doc.data();

      const customizationIds: string[] = serviceData?.customizations || [];

      // Fetch the "customizations" subcollection
      const customizationsRef = collection(db, "services", serviceId, "customizations");
      const customizationsSnap = await getDocs(customizationsRef);

      const customizationsArray = customizationsSnap.docs
        .filter((d) => customizationIds.includes(d.id))
        .map((d) => ({
          id: d.id,
          ...d.data(),
        }));

      serviceDetails.push({
        id: serviceId,
        ...serviceData,
        user_id: userId,
        currency,
        customizations_array: customizationsArray,
      });
    }
  }

  return serviceDetails;
};

// Helper function to fetch services in batches of 10 using "where in"
const _fetchServicesInBatches = async (serviceIds: string[], batchSize = 10) => {
  const { db } = await initFirebase();

  const serviceDetails: any[] = [];

  for (let i = 0; i < serviceIds.length; i += batchSize) {
    const batchIds = serviceIds.slice(i, i + batchSize);

    // Firestore query to get services where the ID is in batchIds
    const servicesQuery = query(
      collection(db, "services"),
      where("__name__", "in", batchIds),
      orderBy("created_at", "desc")
    );
    const querySnapshot = await getDocs(servicesQuery);

    querySnapshot.forEach((doc) => {
      serviceDetails.push({ id: doc.id, ...doc.data() });
    });
  }

  return serviceDetails;
};



interface GetFiltersInput {
      category?:string[] , 
      user_id?:string[] , 
      location?:string[] , 
      date_of_publishing?:PublishingDateFilter
}



const FilterServicesWrapper = async ({
  filters , 
  services
}:{
    filters:GetFiltersInput , 
  services:Array<Service | any>
}) =>{ 
      /**
     *  filter stage
     */
    ///-------------------------------------------------------------------------------------
    let enrichedServices = services;
      let finalResp:Service[] = [];
    let filterApplied = false;

    if(filters?.date_of_publishing) { 
        enrichedServices = filterPostsByPublishingDate(enrichedServices,filters?.date_of_publishing,"services") as Service[];
    }

      if(filters?.category?.length || filters?.location?.length || filters?.user_id?.length ) { 
        filterApplied = true;
        for(let i = 0 ; i < enrichedServices?.length; i++) { 
          let current = enrichedServices?.[i];
          
        // category filter
        if(filters?.category?.length) {
          if(filters?.category?.includes("Literature")){
            filters.category = [...filters?.category , "Storytelling"];
          } 
          if(filters?.category?.includes(current?.category)) { 
              finalResp?.push(current);
          }
        }

        // location filter ❌ not applied
        if(filters?.location?.length) { 
          // @ts-ignore
          if(filters?.location.find((c)=>current?.location?.includes(c))) { 
              finalResp?.push(current);
          }
        }

        // user_id
 
        if(filters?.user_id?.length) { 
          if(current?.user_id && filters?.user_id?.includes(current?.user_id)) { 
             finalResp?.push(current);
          }
        }

        }
      }      
      
      if(!filterApplied) { 
        finalResp = enrichedServices;
      }

    

    ///-------------------------------------------------------------------------------------
      return finalResp;
}


//🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀   use this for getting service by category
export const getServicesByCategory = async (category_name: string, currentUserId?: string , filters?:Omit<GetFiltersInput , "category" | "location">) => {
  try {
    const { db } = await initFirebase();
    console.log(`Fetching services for category: ${category_name}, userId: ${currentUserId}`);

    // Handle My Feed category - which shows services from bookmarked users
    if (category_name === "My Feed") {
      if (!currentUserId) {
        console.log("No currentUserId provided for My Feed");
        return { success: true, services: [] };
      }

      const currentUserRef = doc(db, "users", currentUserId);
      const currentUserSnap = await getDoc(currentUserRef);

      if (!currentUserSnap.exists()) {
        console.log(`User document not found for ID: ${currentUserId}`);
        return { success: true, services: [] };
      }

      const currentUserData = currentUserSnap.data();
      const userIds = currentUserData.bookmarks || [];
      console.log(`Found ${userIds.length} bookmarked users`);

      if (userIds.length === 0) {
        return { success: true, services: [] };
      }

      // Process bookmarked users in batches of 10 (Firestore limit for 'in' queries)
      const batchSize = 10;
      let allServices: string | any[] = [];

      for (let i = 0; i < userIds.length; i += batchSize) {
        const batchUserIds = userIds.slice(i, i + batchSize);
        console.log(`Processing batch ${i / batchSize + 1} with ${batchUserIds.length} users`);

        // Get users first to retrieve their currency information
        const usersQuery = query(collection(db, "users"), where(documentId(), "in", batchUserIds));
        const userSnapshots = await getDocs(usersQuery);
        console.log(`Found ${userSnapshots.size} user documents in this batch`);

        // Create maps to store user currencies and service associations
        const userCurrencyMap: Record<string, string> = {};
        const batchServiceIds: string[] = [];
        const userServiceMap: Record<string, string> = {};

        userSnapshots.forEach((userDoc) => {
          const userData = userDoc.data();
          const userId = userDoc.id;
          const userCurrency = userData?.currency || "gbp";

          // Store each user's currency
          if (userData.services && Array.isArray(userData.services)) {
            console.log(`User ${userId} has ${userData.services.length} services`);
            userData.services.forEach((serviceId: string) => {
              batchServiceIds.push(serviceId);
              userServiceMap[serviceId] = userId;
              userCurrencyMap[serviceId] = userCurrency;
            });
          } else {
            console.log(`User ${userId} has no services array or it's not valid`);
          }
        });

        if (batchServiceIds.length === 0) {
          console.log("No service IDs found in this batch");
          continue;
        }

        // Process service IDs in sub-batches (Firestore limit)
        for (let j = 0; j < batchServiceIds.length; j += batchSize) {
          const subBatchServiceIds = batchServiceIds.slice(j, j + batchSize);

          // Fetch the services
          const servicesQuery = query(
            collection(db, "services"),
            where(documentId(), "in", subBatchServiceIds)
          );
          const servicesSnapshot = await getDocs(servicesQuery);
          console.log(`Found ${servicesSnapshot.size} services in sub-batch ${j / batchSize + 1}`);

          // Map the services with their user info and currency
          const batchServices = servicesSnapshot.docs.map((doc) => {
            const serviceId = doc.id;
            return {
              id: serviceId,
              ...doc.data(),
              user_id: userServiceMap[serviceId],
              currency: userCurrencyMap[serviceId],
            };
          });

          allServices = [...allServices, ...batchServices];
        }
      }
        if(filters) { 
      allServices = await FilterServicesWrapper({filters,services:allServices});
    }

      console.log(`Total services found for My Feed: ${allServices.length}`);
      return { success: true, services: allServices };
    }

    // Original code for other categories
    // Step 1: Query users who have services with the requested category
    const usersQuery = query(collection(db, "users"));
    const userSnapshots = await getDocs(usersQuery);

    if (userSnapshots.empty) {
      console.log("No users found");
      return { success: true, services: [] };
    }

    // Step 2: Collect service IDs and track which user they belong to
    const allServiceIds: string[] = [];
    const userIdsMap: Record<string, string> = {};
    const userCurrencyMap: Record<string, string> = {};

    userSnapshots.forEach((userDoc) => {
      const userData = userDoc.data();
      const userId = userDoc.id;

      const userCurrency = userData?.currency || "gbp";

      // Check if the user has services
      if (userData.services && Array.isArray(userData.services)) {
        userData.services.forEach((serviceId: string) => {
          allServiceIds.push(serviceId);
          userIdsMap[serviceId] = userId;
          userCurrencyMap[serviceId] = userCurrency;
        });
      }
    });

    if (allServiceIds.length === 0) {
      console.log("No service IDs found");
      return { success: true, services: [] };
    }

    let categories = ["Storytelling", "Literature"].includes(category_name)
      ? ["Storytelling", "Literature"]
      : [category_name];

    // Step 3: Fetch services and filter by category
    const servicesQuery = query(collection(db, "services"), where("category", "in", categories));

    const servicesSnapshot = await getDocs(servicesQuery);
    console.log(`Found ${servicesSnapshot.size} services for category ${category_name}`);

    if (servicesSnapshot.empty) {
      return { success: true, services: [] };
    }

    // Step 4: Map the services with their IDs and add user information
    let services:any = servicesSnapshot.docs.map((doc) => {
      const serviceId = doc.id;
      return {
        id: serviceId,
        ...doc.data(),
        user_id: userIdsMap[serviceId],
        currency: userCurrencyMap[serviceId],
      };
    });

    if(filters) { 
      services = await FilterServicesWrapper({filters,services});
    }
    

    return { success: true, services };
  } catch (error) {
    console.error("Error fetching services by category:", error);
    return { success: false, error: "Failed to fetch services" };
  }
};

// updated check
export const getServicesForUsers = async (userIds: string[]) => {
  try {
    const { db } = await initFirebase();

    if (userIds.length === 0) {
      return { success: true, services: [] };
    }

    // Step 1: Fetch all service IDs from user documents
    const allServiceIds: string[] = [];
    const serviceUserMap: Record<string, string> = {}; // Map serviceId -> userId

    const userPromises = userIds.map(async (userId) => {
      const userRef = doc(db, "users", userId);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        const userServiceIds = userData?.services ?? []; // Assuming 'services' array exists
        // console.log({ userServiceIds, userId });

        userServiceIds.forEach((serviceId: string) => {
          serviceUserMap[serviceId] = userId; // Store service-user relationship
        });

        allServiceIds.push(...userServiceIds);
      }
    });

    console.log({ allServiceIds });

    await Promise.all(userPromises); // Wait for all user service retrieval

    if (allServiceIds.length === 0) {
      return { success: true, services: [] };
    }

    // Step 2: Fetch service details in batches
    const serviceDetails = await _fetchServicesInBatchesForUsers(allServiceIds, serviceUserMap);

    return { success: true, services: serviceDetails };
  } catch (error) {
    console.error("Error fetching services for users:", error);
    return { success: false, error: "Failed to fetch services" };
  }
};

// const _fetchServicesInBatchesForUsers = async (
//   serviceIds: string[],
//   serviceUserMap: Record<string, string>,
//   batchSize = 10
// ) => {
//   const serviceDetails: any[] = [];
//   const {db} =  await initFirebase();

//   for (let i = 0; i < serviceIds.length; i += batchSize) {
//     const batchIds = serviceIds.slice(i, i + batchSize);
//     // console.log({ batchIds });

//     // Firestore query to get services where ID is in batchIds
//     const servicesQuery = query(
//       collection(db, "services"),
//       where("__name__", "in", batchIds),
//       orderBy("created_at", "desc")
//     );

//     const querySnapshot = await getDocs(servicesQuery);
//     // console.log({ querySnapshot });

//     querySnapshot.forEach((doc) => {
//       serviceDetails.push({
//         id: doc.id,
//         ...doc.data(),
//         user_id: serviceUserMap[doc.id],
//       });
//       // console.log(doc.data());
//     });
//   }
//   // console.log({ serviceDetails });
//   return serviceDetails;
// };

const _fetchServicesInBatchesForUsers = async (
  serviceIds: string[],
  serviceUserMap: Record<string, string>,
  batchSize = 10
) => {
  const serviceDetails: any[] = [];
  const { db } = await initFirebase();

  for (let i = 0; i < serviceIds.length; i += batchSize) {
    const batchIds = serviceIds.slice(i, i + batchSize);

    const servicesQuery = query(collection(db, "services"), where("__name__", "in", batchIds));

    const querySnapshot = await getDocs(servicesQuery);

    // Fetch customizations in parallel
    const customizationPromises = querySnapshot.docs.map(async (doc) => {
      const serviceId = doc.id;
      const serviceData = doc.data();
      const customizationIds: string[] = serviceData?.customizations || [];

      const customizationsRef = collection(db, "services", serviceId, "customizations");
      const customizationsSnap = await getDocs(customizationsRef);

      const customizationsArray = customizationsSnap.docs
        .filter((d) => customizationIds.includes(d.id)) // only keep the ones in the array
        .map((c) => ({
          id: c.id,
          ...c.data(),
        }));

      return {
        id: serviceId,
        ...doc.data(),
        user_id: serviceUserMap[serviceId],
        customizations_array: customizationsArray,
      };
    });

    const servicesWithCustomizations = await Promise.all(customizationPromises);
    serviceDetails.push(...servicesWithCustomizations);
  }

  return serviceDetails;
};

// Update an existing service
export const updateService = async (id: string, updatedData: any) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      return { success: false };
    }

    //////////////////////////////////////////////////////////// Authorization check
    const usersCollection = collection(db, "users");
    const q = query(usersCollection, where("services", "array-contains", id));
    const userSnap = await getDocs(q);
    if (!userSnap.empty) {
      const userDoc = userSnap.docs[0]; // assuming post ID is unique to one user
      //📋 check if this user if the current authenticated user then only update else return un-auth
      if (userDoc?.data()?.id !== user?.uid) {
        return { success: false };
      }
    }
    ////////////////////////////////////////////////////////////

    const serviceRef = doc(db, "services", id);
    await updateDoc(serviceRef, updatedData);
    return { success: true };
  } catch (error) {
    // console.error("Error updating service:", error);
    return { success: false, error: "Failed to update service" };
  }
};

// Delete a service
export const deleteService = async (id: string) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      return { success: false };
    }

    //////////////////////////////////////////////////////////// Authorization check
    const usersCollection = collection(db, "users");
    const q = query(usersCollection, where("services", "array-contains", id));
    const userSnap = await getDocs(q);
    if (!userSnap.empty) {
      const userDoc = userSnap.docs[0]; // assuming post ID is unique to one user
      //📋 check if this user if the current authenticated user then only update else return un-auth
      if (userDoc?.data()?.id !== user?.uid) {
        return { success: false };
      }
    }
    ////////////////////////////////////////////////////////////

    const serviceRef = doc(db, "services", id);
    await deleteDoc(serviceRef);

    //////////////////////////////////////////////////////////// Remove service ID from user's services array
    const userRef = doc(db, "users", user.uid);
    await updateDoc(userRef, {
      services: arrayRemove(id),
    });

    return { success: true };
  } catch (error) {
    // console.error("Error deleting service:", error);
    return { success: false, error: "Failed to delete service" };
  }
};

export const formatDuration = (
  hours: any,
  format: {
    dayLabel?: string;
    hourLabel?: string;
    handlePlural?: boolean;
  } = {}
) => {
  // Convert hours to a number in case it's a string
  const hoursNum = Number(hours);

  // Handle invalid input
  if (isNaN(hoursNum) || hoursNum < 0) {
    return "Invalid duration";
  }

  // If hours is 0, return 0
  if (hoursNum === 0) {
    return "0";
  }

  // Calculate days and remaining hours
  const days = Math.floor(hoursNum / 8);
  const remainingHours = hoursNum % 8;

  // Default labels
  const baseDayLabel = format.dayLabel || "d";
  const baseHourLabel = format.hourLabel || "h";

  // Handle pluralization if requested
  const handlePlural = format.handlePlural !== undefined ? format.handlePlural : false;

  // Format day label with pluralization if needed
  let dayLabel = baseDayLabel;
  if (handlePlural && baseDayLabel.trim() === "day") {
    dayLabel = days === 1 ? " Day" : " Days";
  }

  // Format hour label with pluralization if needed
  let hourLabel = baseHourLabel;
  if (handlePlural && baseHourLabel.trim() === "hour") {
    hourLabel = remainingHours === 1 ? " Hour" : " Hours";
  }

  // Format parts
  const dayPart = days > 0 ? `${days}${dayLabel}` : "";
  const hourPart = remainingHours > 0 ? `${remainingHours}${hourLabel}` : "";

  // Join parts with a space if both exist
  if (dayPart && hourPart) {
    return `${dayPart} ${hourPart}`;
  }

  // Return whichever part exists, or "0" if neither does
  return dayPart || hourPart;
};

export const listPricecal = (price: any) => {
  let serviceCost;
  if (!price) {
    return 0;
  }
  if (price) {
    const listPrice = parseFloat(price);
    serviceCost = parseFloat((listPrice / (1 - 0.16)).toFixed(2));
  }
  return `${Number(serviceCost)}`;
};

// updated
export const deleteCustomization = async (serviceId: string, customizationId: string) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    //////////////////////////////////////////////////////////// Authorization check
    const usersCollection = collection(db, "users");
    const q = query(usersCollection, where("services", "array-contains", serviceId));
    const userSnap = await getDocs(q);
    if (!userSnap.empty) {
      const userDoc = userSnap.docs[0]; // assuming post ID is unique to one user
      //📋 check if this user if the current authenticated user then only update else return un-auth
      if (userDoc?.data()?.id !== user?.uid) {
        return { success: false };
      }
    }
    ////////////////////////////////////////////////////////////

    const serviceRef = doc(db, "services", serviceId);

    await updateDoc(serviceRef, {
      customizations: arrayRemove(customizationId),
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: "Failed to delete customization" };
  }
};

export const updateCustomizations = async (
  serviceId: string,
  customizations: CustomizationInput[]
) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    const customizationsRef = collection(db, "services", serviceId, "customizations");
    const newCustomizationIds: string[] = [];

    const promises = customizations.map(async (c: any) => {
      if (c.id) {
        // If customization has an ID, update existing
        const customizationDocRef = doc(customizationsRef, c.id);
        await setDoc(
          customizationDocRef,
          {
            ...c,
            updated_at: serverTimestamp(),
          },
          { merge: true }
        );
        newCustomizationIds.push(c.id);
      } else {
        // If no ID, create a new customization
        const newCustomizationDocRef = await addDoc(customizationsRef, {
          ...c,
          updated_at: serverTimestamp(),
        });
        newCustomizationIds.push(newCustomizationDocRef.id);
      }
    });

    await Promise.all(promises);

    // Update the service document's customizations array
    const serviceRef = doc(db, "services", serviceId);
    await setDoc(serviceRef, { customizations: newCustomizationIds }, { merge: true });

    return { success: true };
  } catch (error) {
    console.error("Error updating customizations:", error);
    return { success: false, error: "Failed to update customizations" };
  }
};
