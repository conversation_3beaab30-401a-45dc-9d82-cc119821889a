import React, { useState } from "react";
import { CheckSquare, Square, HelpCircle, ChevronDown, Loader } from "react-feather";
import { <PERSON><PERSON>, <PERSON>dalContent, ModalBody } from "@heroui/react";
import { But<PERSON> } from "../ui/button";
import { BasketItem } from "../../types/basket";

interface ConfirmPaymentProps {
  selectedItem: BasketItem | null;
  onConfirm: () => void;
  currencySymbol?: string;
  profileDetails?: {
    email?: any;
    stripe_id?: any;
    id: string;
    avatar?: string;
    profile_name?: string;
    currency?: string;
  } | null;
  userProfileDetails?: {
    email?: any;
    profile_name?: any;
    id: string;
  } | null;
  formatDate: any;
}

interface PaymentMethod {
  id: "wallet" | "card" | "crypto";
  name: string;
  icon: React.ReactNode;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: "wallet",
    name: "Wallets (Apple Pay / Google Pay)",
    icon: <img src="/assets/stripe.svg" alt="" className="w-4 h-4 object-cover" />,
  },
  {
    id: "card",
    name: "Card/PayPal (Escrow)",
    icon: <img src="/assets/stripe.svg" alt="" className="w-4 h-4 object-cover" />,
  },
  {
    id: "crypto",
    name: "Crypto (Stripe)",
    icon: <span className="text-orange-500">🪙</span>,
  },
];

const ConfirmPayment: React.FC<ConfirmPaymentProps> = ({
  selectedItem,
  onConfirm,
  currencySymbol = "$",
  profileDetails,
  userProfileDetails,
  formatDate,
}) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [checked, setChecked] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(
    paymentMethods[1]
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null); // new state for error
  console.log(selectedItem);

  if (!selectedItem) return null;

  const transactionFee = selectedItem.subtotal * 0.04;
  const total = selectedItem.subtotal + transactionFee;

  const handlePaymentConfirm = async () => {
    try {
      setIsLoading(true);
      setErrorMessage(null); // reset old error

      if (!selectedItem || !profileDetails || !userProfileDetails) {
        setErrorMessage("Missing required payment details. Please refresh and try again.");
        setIsLoading(false);
        return;
      }

      const baseData = {
        userId: userProfileDetails.id,
        userEmail: userProfileDetails.email,
        sellerId: profileDetails.id,
        orderId: selectedItem.orderId || selectedItem.id.toString(),
        amount: Math.round(total * 100),
        currency: profileDetails.currency || "usd",
        productName: selectedItem.title,
      };

      if (selectedPaymentMethod.id === "crypto") {
        const paymentUrl =
          `/payment/buy-from-seller?` +
          new URLSearchParams({
            amount: baseData.amount.toString(),
            currency: baseData.currency,
            productName: baseData.productName,
            sellerId: baseData.sellerId,
            orderId: baseData.orderId,
            paymentMethod: "crypto",
            isEscrow: "false",
            userId: baseData.userId,
          }).toString();
        window.location.href = paymentUrl;
      } else {
        const escrowData = {
          ...baseData,
          sellerEmail: profileDetails.email,
          sellerStripeAccountId: profileDetails.stripe_id,
          productDescription: selectedItem.description || selectedItem.title || "Service order",
        };

        const response = await fetch("/api/escrow/create", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(escrowData),
        });
        const result = await response.json();

        if (response.ok && result?.success) {
          const paymentUrl =
            `/payment/buy-from-seller?` +
            new URLSearchParams({
              amount: escrowData.amount.toString(),
              currency: escrowData.currency,
              productName: escrowData.productName,
              sellerId: escrowData.sellerId,
              orderId: escrowData.orderId,
              paymentMethod: selectedPaymentMethod.id === "wallet" ? "wallet" : "card",
              isEscrow: "true",
              userId: escrowData.userId,
              clientSecret: result.clientSecret,
              paymentIntentId: result.paymentIntentId,
              transactionId: result.transactionId,
              useExisting: "true",
              isInvoice: checked.toString(),
              formateDate: formatDate,
            }).toString();
          window.location.href = paymentUrl;
        } else {
          setErrorMessage(result?.error || "Could not start escrow checkout. Please try again.");
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error("❌ Error in handlePaymentConfirm:", error);
      setErrorMessage("Unexpected error occurred. Please try again.");
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col mr-4">
      {/* Summary */}
      <div className="flex justify-between">
        <p className="text-[#404040] text-sm">Subtotal</p>
        <p className="font-bold text-primary text-sm">
          {currencySymbol}
          {selectedItem.subtotal.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between my-1">
        <p className="text-[#404040] text-sm">Transaction fee (4%)</p>
        <p className="font-bold text-primary text-sm">
          {currencySymbol}
          {transactionFee.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between mb-4">
        <p className="text-[#404040] text-sm">Order total</p>
        <p className="font-bold text-primary text-sm">
          {currencySymbol}
          {total.toFixed(2)}
        </p>
      </div>

      <div className="flex justify-between text-subtitle mb-2">
        <p className="text-sm">Delivery time</p>
        <p className="font-semibold text-sm">{selectedItem.time}</p>
      </div>

      <div className="flex flex-row gap-2 mt-2 border-b-2 pb-3">
        <div onClick={() => setChecked((prev) => !prev)} className="cursor-pointer select-none">
          {checked ? <CheckSquare color="#333333" /> : <Square color="#bdbdbd" />}
        </div>
        <div>
          <p className="text-primary">Request an invoice </p>
          <p className="text-[#898887] text-sm">
            Note: to obtain an Invoice you'll need to provide your tax details (legal name, address
            and VAT registration number).
          </p>
        </div>
      </div>

      <div className="mt-3">
        <p className="text-subtitle text-sm">
          Terms: By placing your order, you confirm that you agree to the User Terms and Conditions.
        </p>
        <button
          onClick={() => {
            setErrorMessage(null);
            setIsPaymentModalOpen(true);
          }}
          className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-4"
        >
          Confirm Payment
        </button>
      </div>

      <Modal
        placement="auto"
        isOpen={isPaymentModalOpen}
        onClose={() => !isLoading && setIsPaymentModalOpen(false)}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content w-xl p-12 rounded-3xl">
          {() => (
            <ModalBody>
              {/* Error Banner */}
              {errorMessage && (
                <div className="mb-4 p-3 rounded-md bg-red-100 text-red-700 border border-red-300 text-sm">
                  {errorMessage}
                </div>
              )}

              <div className="flex gap-2 items-center">
                <p className="font-bold text-lg max-md:font-semibold max-md:text-base">
                  Proceed to payment
                </p>
                <HelpCircle className="h-5 w-5 text-gray-400 transition-transform" />
              </div>

              {/* Payment Method Dropdown */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="w-full bg-white border border-gray-300 rounded-lg px-4 py-2 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center">
                          {selectedPaymentMethod.icon}
                        </div>
                        <span className="text-gray-900">{selectedPaymentMethod.name}</span>
                      </div>
                      <ChevronDown
                        className={`h-5 w-5 text-gray-400 transition-transform ${
                          isDropdownOpen ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                  </button>

                  {isDropdownOpen && (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg">
                      {paymentMethods.map((method) => (
                        <button
                          key={method.id}
                          type="button"
                          onClick={() => {
                            setSelectedPaymentMethod(method);
                            setIsDropdownOpen(false);
                          }}
                          className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3 ${
                            selectedPaymentMethod.id === method.id
                              ? "bg-blue-50 text-primary"
                              : "text-gray-900"
                          } ${method.id === paymentMethods[0].id ? "rounded-t-lg" : ""} ${
                            method.id === paymentMethods[paymentMethods.length - 1].id
                              ? "rounded-b-lg"
                              : ""
                          }`}
                        >
                          <div className="flex items-center justify-center">{method.icon}</div>
                          <span>{method.name}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Button
                  variant="outline"
                  disabled={isLoading}
                  className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base flex items-center justify-center"
                  onClick={handlePaymentConfirm}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader size={48} className="text-primary animate-spin" />
                      Processing...
                    </div>
                  ) : (
                    "Yes, confirm"
                  )}
                </Button>
                <Button
                  variant="outline"
                  disabled={isLoading}
                  className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                  onClick={() => setIsPaymentModalOpen(false)}
                >
                  No, cancel
                </Button>
              </div>
            </ModalBody>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ConfirmPayment;
